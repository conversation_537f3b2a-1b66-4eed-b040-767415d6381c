{"versions": [{"version": "22.04", "codename": "jammy", "architectures": ["amd64", "arm64"], "packages": "file curl wget vim net-tools iputils-ping ca-certificates apt-transport-https gnupg lsb-release", "crypto_version": "1.0.4"}, {"version": "20.04", "codename": "focal", "architectures": ["amd64", "arm64"], "packages": "file curl wget vim net-tools iputils-ping ca-certificates apt-transport-https gnupg lsb-release", "crypto_version": "1.0.4"}, {"version": "18.04", "codename": "bionic", "architectures": ["amd64", "arm64"], "packages": "file curl wget vim net-tools iputils-ping ca-certificates apt-transport-https gnupg lsb-release", "crypto_version": "1.0.4"}], "default_version": "22.04", "default_arch": "amd64", "default_crypto_version": "1.0.4", "build_defaults": {"namespace": "btit", "repository": "infra/os"}}