#!/bin/bash
# 测试包安装修复的脚本

echo "=== 测试包安装修复 ==="

# 检查 os_versions.json 中的包列表
echo "1. 检查 os_versions.json 中的包列表:"
echo "Ubuntu 22.04 包列表:"
jq -r '.versions[] | select(.version == "22.04") | .packages' docker/scripts/build/versions/os_versions.json

echo ""
echo "Ubuntu 20.04 包列表:"
jq -r '.versions[] | select(.version == "20.04") | .packages' docker/scripts/build/versions/os_versions.json

echo ""
echo "Ubuntu 18.04 包列表:"
jq -r '.versions[] | select(.version == "18.04") | .packages' docker/scripts/build/versions/os_versions.json

# 检查 Dockerfile 模板
echo ""
echo "2. 检查 Dockerfile 模板中的包安装部分:"
echo "查看 apt-get install 命令:"
grep -A 25 "apt-get install" docker/infrastructure/os/ubuntu/Dockerfile.template

echo ""
echo "=== 修复摘要 ==="
echo "✓ 在 os_versions.json 的所有版本中添加了 'file' 包"
echo "✓ 从 Dockerfile.template 中移除了重复的包:"
echo "  - 移除了 'vim-tiny'（使用 packages 中的 'vim'）"
echo "  - 移除了 'file'（现在在 packages 中）"
echo "  - 移除了 'curl'（现在在 packages 中）"
echo "  - 移除了 'ca-certificates'（现在在 packages 中）"
echo "  - 移除了 'apt-transport-https'（现在在 packages 中）"

echo ""
echo "=== 测试建议 ==="
echo "现在可以重新构建镜像来测试修复:"
echo "  ./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --no-cache --debug --no-push"
echo ""
echo "这将:"
echo "- 显示实际的包列表（调试模式）"
echo "- 强制重新构建（无缓存）"
echo "- 验证 file 命令是否可用"
