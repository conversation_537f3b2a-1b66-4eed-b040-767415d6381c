# Kylin 加密方式最终修正

## 问题分析

从错误日志 `[ ubuntu = kylin ]` 可以看出，Dockerfile 中的判断逻辑仍然在使用 `OS_TYPE` 变量进行比较，但构建脚本没有传递这个参数，导致判断失败。

## 最终解决方案

保持 `OS_TYPE` 参数用于标识操作系统类型，同时新增 `USE_KYLIN_CRYPTO` 参数用于控制加密安装方式。

### 1. 构建脚本修正

**文件**: `docker/scripts/build/build-os.sh`

#### 构建参数传递
```bash
local build_args=(
    "--platform" "linux/$arch"
    "--build-arg" "CRYPTO_VERSION=$CRYPTO_VERSION"
    "--build-arg" "ARCH=$arch"
    "--build-arg" "OS_TYPE=$os_type"              # 传递操作系统类型
    "--build-arg" "USE_KYLIN_CRYPTO=$USE_KYLIN_CRYPTO"  # 传递加密方式
    "--build-arg" "INSTALL_CARD_MNGR=$INSTALL_CARD_MNGR"
    "--build-arg" "INSTALL_SC62=$INSTALL_SC62"
    "--build-arg" "INSTALL_SC34=$INSTALL_SC34"
    "-t" "$image_tag"
)
```

#### 新增参数处理
```bash
# 新增变量
USE_KYLIN_CRYPTO=false

# 新增参数解析
--use-kylin-crypto)
    USE_KYLIN_CRYPTO=true
    shift
    ;;
```

### 2. Dockerfile 模板修正

**文件**: `docker/infrastructure/os/ubuntu/Dockerfile.template`

#### 构建参数定义
```dockerfile
ARG OS_TYPE=ubuntu
ARG USE_KYLIN_CRYPTO=false
```

#### 安装逻辑修正
```dockerfile
# 安装NETCA CRYPTO
&& if [ "$USE_KYLIN_CRYPTO" = "true" ]; then \
     echo "使用Kylin方式安装NETCA_CRYPTO，使用预编译的加密库文件..." \
     && echo "正在复制Kylin加密库文件到/usr/lib64/..." \
     && find /tmp/kylin-crypto-files -name "*.so*" -exec cp {} /usr/lib64/ \; \
     && chmod 755 /usr/lib64/*.so* \
     && echo "Kylin加密库文件安装完成"; \
   else \
     echo "使用标准NETCA CRYPTO安装流程..." \
     && cd /tmp/NETCA_CRYPTO_linux32_64 \
     && chmod +x setup.sh \
     && echo "开始安装NETCA CRYPTO组件..." \
     && bash -x ./setup.sh /usr/lib64 CRYPTO move; \
   fi \
```

#### 密码卡组件安装修正
```dockerfile
# 根据条件安装密码卡管理JNI库（仅在标准模式下）
&& if [ "$INSTALL_CARD_MNGR" = "true" ] && [ "$USE_KYLIN_CRYPTO" != "true" ]; then \
     echo "开始安装NetcaCardMngr组件..." \
     && cd /tmp/NETCA_CRYPTO_linux32_64 \
     && bash -x ./setup.sh /usr/lib64 NetcaCardMngr move; \
   fi \
```

**说明**: 密码卡组件只在标准模式下安装，因为 Kylin 模式使用预编译的 .so 文件，不需要 setup.sh 脚本。

## 参数说明

### OS_TYPE 参数
- **用途**: 标识操作系统类型
- **取值**: ubuntu, alpine, centos, rhel, openeuler
- **传递方式**: 构建脚本自动传递，值为第一个位置参数

### USE_KYLIN_CRYPTO 参数
- **用途**: 控制 NETCA_CRYPTO 的安装方式
- **取值**: true (使用 Kylin 方式), false (使用标准方式)
- **传递方式**: 通过 `--use-kylin-crypto` 参数控制

## 使用方法

### 标准安装方式
```bash
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --no-cache --debug --no-push
```

### Kylin 加密方式
```bash
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --no-cache --debug --no-push --use-kylin-crypto
```

### 组合使用（注意限制）
```bash
# 这个命令会忽略密码卡组件，因为 Kylin 模式不支持
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --use-kylin-crypto --install-all-cards --no-push
```

## 镜像标签

- 标准模式: `ubuntu:22.04`
- Kylin 模式: `ubuntu:22.04-kylin-crypto`
- 标准模式+密码卡: `ubuntu:22.04-crypto-sc62`

## 构建日志预期

### Kylin 模式成功日志
```
构建配置:
操作系统: ubuntu
版本: 22.04
架构: arm64
使用Kylin加密方式: true

+ [ false = true ]
+ echo 使用Kylin方式安装NETCA_CRYPTO，使用预编译的加密库文件...
使用Kylin方式安装NETCA_CRYPTO，使用预编译的加密库文件...
+ echo 正在复制Kylin加密库文件到/usr/lib64/...
正在复制Kylin加密库文件到/usr/lib64/...
+ find /tmp/kylin-crypto-files -name '*.so*' -exec cp {} /usr/lib64/ \;
+ chmod 755 /usr/lib64/*.so*
+ echo Kylin加密库文件安装完成
Kylin加密库文件安装完成
```

### 标准模式成功日志
```
+ [ false = true ]
+ echo 使用标准NETCA CRYPTO安装流程...
使用标准NETCA CRYPTO安装流程...
+ cd /tmp/NETCA_CRYPTO_linux32_64
+ chmod +x setup.sh
+ echo 开始安装NETCA CRYPTO组件...
开始安装NETCA CRYPTO组件...
+ bash -x ./setup.sh /usr/lib64 CRYPTO move
```

## 总结

此次修正解决了以下问题：

1. **概念澄清**: `kylin` 是加密安装方式，不是操作系统类型
2. **参数分离**: `OS_TYPE` 用于操作系统类型，`USE_KYLIN_CRYPTO` 用于加密方式
3. **逻辑修正**: 密码卡组件只在标准模式下安装
4. **错误修复**: 解决了 `[ ubuntu = kylin ]` 判断失败的问题

现在构建命令应该能正确工作，Kylin 模式会使用预编译的 .so 文件，而不会尝试执行不存在的 setup.sh 脚本。
