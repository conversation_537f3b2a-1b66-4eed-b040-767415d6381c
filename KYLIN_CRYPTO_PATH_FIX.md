# Kylin 加密库路径修正

## 问题描述

在构建 Docker 镜像时发现两个路径相关的问题：

1. **Kylin .so 文件路径问题**：Dockerfile.template 中没有按架构复制 Kylin 加密库文件
2. **构建脚本路径问题**：build-os.sh 中 Kylin 文件复制逻辑没有考虑架构子目录

## 修正内容

### 1. 修正 Dockerfile.template

**文件**: `docker/infrastructure/os/ubuntu/Dockerfile.template`

**修改前**:
```dockerfile
# Copy Kylin crypto files if building for Kylin OS
COPY shared/crypto-files/kylin/ /tmp/kylin-crypto-files/
```

**修改后**:
```dockerfile
# Copy Kylin crypto files if building for Kylin OS (按架构复制)
COPY shared/crypto-files/kylin/${ARCH}/ /tmp/kylin-crypto-files/
```

**说明**: 添加了 `${ARCH}` 变量，确保按架构复制对应的 .so 文件。

### 2. 修正构建脚本

**文件**: `docker/scripts/build/build-os.sh`

**修改前**:
```bash
# 复制 Kylin 专用的加密文件
if [ -d "$BASE_DIR/shared/crypto-files/kylin" ]; then
    echo "复制 Kylin 加密库文件..."
    cp -r "$BASE_DIR/shared/crypto-files/kylin/"* "$build_dir/shared/crypto-files/kylin/"
else
    echo -e "${YELLOW}警告: Kylin 加密库文件不存在${NC}"
fi
```

**修改后**:
```bash
# 复制 Kylin 专用的加密文件（按架构复制）
local kylin_source="$BASE_DIR/shared/crypto-files/kylin/$arch"
if [ -d "$kylin_source" ]; then
    echo "复制 Kylin 加密库文件: $kylin_source -> $build_dir/shared/crypto-files/kylin/$arch/"
    mkdir -p "$build_dir/shared/crypto-files/kylin/$arch"
    cp -r "$kylin_source/"* "$build_dir/shared/crypto-files/kylin/$arch/"
else
    echo -e "${YELLOW}警告: Kylin 加密库文件不存在: $kylin_source${NC}"
fi
```

**说明**: 
- 添加了架构相关的源路径变量 `kylin_source`
- 创建对应的目标目录结构
- 按架构复制文件，支持多架构构建

## 目录结构验证

当前 Kylin 加密库文件的目录结构：
```
docker/infrastructure/os/shared/crypto-files/kylin/
└── arm64/
    ├── libNetcaJCrypto.so
    ├── libnetca_asn1.so.1
    ├── libnetca_crypto.so.1
    ├── libnetca_log.so.1
    ├── libnetca_url2.so
    ├── libnetca_util.so.2
    └── libnetca_xml.so.2
```

修正后的复制逻辑将正确处理这种按架构组织的目录结构。

## 影响范围

- ✅ Ubuntu Dockerfile.template 已修正
- ✅ build-os.sh 构建脚本已修正
- ✅ 支持多架构 Kylin 加密库文件复制
- ✅ 保持向后兼容性

## 测试建议

构建测试命令：
```bash
# 测试 ARM64 架构构建
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --no-push --debug

# 测试 AMD64 架构构建  
./docker/scripts/build/build-os.sh ubuntu 22.04 amd64 --no-push --debug
```

构建成功后应该看到：
```
✅ 复制加密组件文件...
✅ 复制 NETCA_CRYPTO 文件: .../1.0.4/arm64
✅ 复制 Kylin 加密库文件: .../kylin/arm64
✅ 镜像构建完成
```

## 总结

此次修正解决了 Kylin 操作系统下 .so 文件路径不正确的问题，确保：

1. **架构感知**: 按目标架构复制对应的加密库文件
2. **路径正确**: Dockerfile 和构建脚本中的路径保持一致
3. **多架构支持**: 同时支持 ARM64 和 AMD64 架构
4. **错误处理**: 当文件不存在时给出明确的警告信息

修正后的代码更加健壮，支持多架构构建，并且路径处理更加准确。
