# Kylin 加密方式参数修正

## 问题描述

原代码中存在概念混淆：将 `kylin` 作为操作系统类型处理，但实际上 `kylin` 应该是 NETCA_CRYPTO 的一种安装方式，而不是操作系统类型。

## 修正内容

### 1. 概念澄清

**修正前的错误理解**：
- `kylin` 被当作操作系统类型（与 ubuntu, centos 等并列）
- 使用 `OS_TYPE=kylin` 来控制安装方式

**修正后的正确理解**：
- `kylin` 是 NETCA_CRYPTO 的一种安装方式（使用预编译的 .so 文件）
- 任何操作系统都可以选择使用 kylin 方式安装 NETCA_CRYPTO

### 2. 构建脚本修正

**文件**: `docker/scripts/build/build-os.sh`

#### 2.1 帮助信息修正
```bash
# 修正前
echo "  os_type  - 操作系统类型 (ubuntu, alpine, centos, rhel, openeuler, kylin)"

# 修正后  
echo "  os_type  - 操作系统类型 (ubuntu, alpine, centos, rhel, openeuler)"
```

#### 2.2 新增 kylin 加密方式参数
```bash
# 新增选项
echo "  --use-kylin-crypto             使用Kylin方式安装NETCA_CRYPTO（预编译.so文件）"

# 新增示例
echo "  $0 ubuntu 22.04 --use-kylin-crypto"
```

#### 2.3 参数解析修正
```bash
# 新增变量
USE_KYLIN_CRYPTO=false

# 新增参数处理
--use-kylin-crypto)
    USE_KYLIN_CRYPTO=true
    shift
    ;;
```

#### 2.4 构建参数修正
```bash
# 修正前
"--build-arg" "OS_TYPE=$os_type"

# 修正后
"--build-arg" "USE_KYLIN_CRYPTO=$USE_KYLIN_CRYPTO"
```

#### 2.5 镜像标签修正
```bash
# 新增 kylin-crypto 后缀逻辑
if [ "$USE_KYLIN_CRYPTO" = "true" ]; then
    image_tag="${image_tag}-kylin-crypto"
fi
```

### 3. Dockerfile 模板修正

**文件**: `docker/infrastructure/os/ubuntu/Dockerfile.template`

#### 3.1 构建参数修正
```dockerfile
# 修正前
ARG OS_TYPE=ubuntu

# 修正后
ARG USE_KYLIN_CRYPTO=false
```

#### 3.2 安装逻辑修正
```dockerfile
# 修正前
&& if [ "$OS_TYPE" = "kylin" ]; then \
     echo "检测到Kylin操作系统，使用预编译的加密库文件..." \

# 修正后
&& if [ "$USE_KYLIN_CRYPTO" = "true" ]; then \
     echo "使用Kylin方式安装NETCA_CRYPTO，使用预编译的加密库文件..." \
```

#### 3.3 注释修正
```dockerfile
# 修正前
# Copy Kylin crypto files if building for Kylin OS (按架构复制)

# 修正后  
# Copy Kylin crypto files if using Kylin crypto method (按架构复制)
```

## 使用方法

### 标准 NETCA_CRYPTO 安装方式
```bash
# 使用标准的 setup.sh 脚本安装
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --no-push
```

### Kylin 加密方式安装
```bash
# 使用预编译的 .so 文件（Kylin 方式）
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --use-kylin-crypto --no-push
```

### 组合使用
```bash
# 同时使用 Kylin 加密方式和密码卡组件
./docker/scripts/build/build-os.sh ubuntu 22.04 arm64 --use-kylin-crypto --install-all-cards --no-push
```

## 镜像标签规则

修正后的镜像标签命名规则：

```bash
# 基础镜像
registry.example.com/btit/infra/os/ubuntu:22.04

# 使用 Kylin 加密方式
registry.example.com/btit/infra/os/ubuntu:22.04-kylin-crypto

# 使用密码卡组件
registry.example.com/btit/infra/os/ubuntu:22.04-crypto-sc62

# 同时使用 Kylin 加密方式和密码卡组件
registry.example.com/btit/infra/os/ubuntu:22.04-kylin-crypto-crypto-sc62
```

## 配置显示

构建时会显示新的配置信息：
```
构建配置:
操作系统: ubuntu
版本: 22.04
架构: arm64
调试模式: false
强制无缓存: false
推送镜像: 否
安装密码卡管理JNI库: false
安装SansecCard_SC62: false
安装SansecCard_SC34: false
使用Kylin加密方式: true
```

## 影响范围

- ✅ 概念澄清：kylin 不再是操作系统类型，而是加密安装方式
- ✅ 参数规范：新增 `--use-kylin-crypto` 参数
- ✅ 向后兼容：保持现有功能不受影响
- ✅ 灵活性增强：任何操作系统都可以选择 kylin 加密方式
- ✅ 标签清晰：镜像标签能明确反映使用的安装方式

## 总结

此次修正解决了概念混淆问题，将 `kylin` 从操作系统类型改为 NETCA_CRYPTO 的安装方式选项。这样的设计更加合理和灵活：

1. **概念清晰**：kylin 是安装方式，不是操作系统类型
2. **使用灵活**：任何操作系统都可以选择使用 kylin 加密方式
3. **参数明确**：`--use-kylin-crypto` 参数语义清晰
4. **标签规范**：镜像标签能准确反映构建配置
